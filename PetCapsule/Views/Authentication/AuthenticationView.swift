//
//  AuthenticationView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import AuthenticationServices

struct AuthenticationView: View {
    @EnvironmentObject var authService: AuthenticationService
    @StateObject private var biometricService = BiometricAuthenticationService()

    @State private var showSignUp = false
    @State private var email = ""
    @State private var password = ""
    @State private var fullName = ""
    @State private var showForgotPassword = false
    @State private var showPrivacyPolicy = false
    @State private var showTermsOfService = false

    var body: some View {
        NavigationView {
            ZStack {
                // Modern Magic UI Background with animated gradient
                MagicGradientBackground()
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 40) {
                        // Modern Logo Section with Magic UI
                        modernLogoSection

                        // Biometric Authentication (if available and enabled)
                        if authService.authenticationState == .biometricRequired {
                            modernBiometricSection
                        } else {
                            // Modern Authentication Section with Magic UI
                            modernAuthenticationSection
                        }

                        // Modern Privacy Section
                        modernPrivacySection

                        // Development Skip Option
                        if Config.Features.skipAuthentication {
                            modernDevelopmentSkipSection
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 40)
                }
            }
            .navigationBarHidden(true)
            .alert("Authentication Error", isPresented: .constant(authService.errorMessage != nil)) {
                Button("OK") {
                    authService.errorMessage = nil
                }
            } message: {
                Text(authService.errorMessage ?? "")
            }
            .sheet(isPresented: $showForgotPassword) {
                ForgotPasswordView()
                    .environmentObject(authService)
            }
            .sheet(isPresented: $showPrivacyPolicy) {
                SimplePrivacyPolicyView()
            }
            .sheet(isPresented: $showTermsOfService) {
                SimpleTermsOfServiceView()
            }
        }
    }

    // MARK: - Modern Magic UI Logo Section

    private var modernLogoSection: some View {
        VStack(spacing: 24) {
            // Modern App Icon with Magic UI Effects
            ZStack {
                // Outer glow effect
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.purple.opacity(0.3),
                                Color.blue.opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 50,
                            endRadius: 120
                        )
                    )
                    .frame(width: 140, height: 140)
                    .blur(radius: 10)

                // Main icon container with glassmorphism
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.25),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [Color.white.opacity(0.6), Color.white.opacity(0.2)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)

                // Paw print icon with gradient
                Image(systemName: "pawprint.fill")
                    .font(.system(size: 48, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.purple, Color.blue, Color.cyan],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }

            // Modern title with enhanced typography
            VStack(spacing: 12) {
                Text("PetTime Capsule")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.primary, Color.primary.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                Text("Preserve precious moments with your beloved pets using AI-powered memory curation")
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.top, 20)
    }

    // MARK: - Modern Biometric Section

    private var modernBiometricSection: some View {
        MagicCard {
            VStack(spacing: 20) {
                // Biometric icon with animation
                ZStack {
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [Color.blue.opacity(0.3), Color.clear],
                                center: .center,
                                startRadius: 20,
                                endRadius: 60
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: biometricService.biometricType.icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }

                VStack(spacing: 8) {
                    Text("Secure Authentication")
                        .font(.system(size: 20, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("Use \(biometricService.biometricType.displayName) to securely access your pet memories")
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                MagicButton(
                    title: "Authenticate with \(biometricService.biometricType.displayName)",
                    icon: biometricService.biometricType.icon,
                    style: .secondary,
                    isLoading: authService.isLoading,
                    action: authenticateWithBiometrics
                )

                Button("Use different account") {
                    authService.authenticationState = .unauthenticated
                }
                .font(.system(size: 14, weight: .medium, design: .rounded))
                .foregroundColor(.purple)
            }
        }
    }

    // MARK: - Biometric Authentication Section (Legacy)

    private var biometricAuthenticationSection: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: biometricService.biometricType.icon)
                    .font(.system(size: 60))
                    .foregroundColor(.blue)

                Text("Welcome Back!")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Use \(biometricService.biometricType.displayName) to securely access your pet data")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            VStack(spacing: 16) {
                // Biometric Authentication Button
                Button(action: authenticateWithBiometrics) {
                    HStack {
                        if authService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        } else {
                            Image(systemName: biometricService.biometricType.icon)
                                .font(.title3)
                        }
                        Text("Authenticate with \(biometricService.biometricType.displayName)")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .shadow(color: .blue.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .disabled(authService.isLoading)

                // Alternative Sign In Button
                Button("Sign in with different account") {
                    authService.authenticationState = .unauthenticated
                }
                .foregroundColor(.blue)
                .font(.body)
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Modern Authentication Section

    private var modernAuthenticationSection: some View {
        VStack(spacing: 24) {
            // Modern Apple Sign In Button with Magic UI
            MagicButton(
                title: "Continue with Apple",
                icon: "applelogo",
                style: .primary,
                action: {
                    Task {
                        await authService.signInWithApple()
                    }
                }
            )

            // Modern divider with glassmorphism
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.clear, Color.white.opacity(0.3), Color.clear],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Text("or")
                    .font(.system(size: 14, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)

                Rectangle()
                    .frame(height: 1)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.clear, Color.white.opacity(0.3), Color.clear],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            }

            // Modern email/password form
            VStack(spacing: 20) {
                if showSignUp {
                    MagicTextField(
                        placeholder: "Full Name",
                        text: $fullName,
                        icon: "person.fill"
                    )
                }

                MagicTextField(
                    placeholder: "Email",
                    text: $email,
                    icon: "envelope.fill",
                    keyboardType: .emailAddress
                )

                MagicTextField(
                    placeholder: "Password",
                    text: $password,
                    icon: "lock.fill",
                    isSecure: true
                )

                // Modern sign in/up button
                MagicButton(
                    title: showSignUp ? "Create Account" : "Sign In",
                    style: .secondary,
                    isLoading: authService.isLoading,
                    isEnabled: isFormValid,
                    action: performEmailAuthentication
                )

                // Toggle sign up/in
                Button(action: { showSignUp.toggle() }) {
                    Text(showSignUp ? "Already have an account? Sign In" : "Don't have an account? Sign Up")
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(.purple)
                }
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Main Authentication Section (Legacy)

    private var authenticationSection: some View {
        VStack(spacing: 24) {
            // Apple Sign In Button
            SignInWithAppleButton(
                onRequest: { request in
                    // Configure Apple Sign In request
                },
                onCompletion: { result in
                    // Handle Apple Sign In result
                    Task {
                        await authService.signInWithApple()
                    }
                }
            )
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50)
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)

            // Divider
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.gray.opacity(0.3))

                Text("or")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)

                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.gray.opacity(0.3))
            }

            // Email Authentication Form
            VStack(spacing: 16) {
                if showSignUp {
                    CustomTextField(
                        title: "Full Name",
                        text: $fullName,
                        icon: "person.fill"
                    )
                }

                CustomTextField(
                    title: "Email",
                    text: $email,
                    icon: "envelope.fill",
                    keyboardType: .emailAddress
                )

                CustomSecureField(
                    title: "Password",
                    text: $password,
                    icon: "lock.fill"
                )

                if !showSignUp {
                    HStack {
                        Spacer()
                        Button("Forgot Password?") {
                            showForgotPassword = true
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
            }

            // Sign In/Up Button
            Button(action: performEmailAuthentication) {
                HStack {
                    if authService.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    }
                    Text(showSignUp ? "Create Account" : "Sign In")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(isFormValid ? Color.blue : Color.gray.opacity(0.3))
                .foregroundColor(.white)
                .cornerRadius(12)
                .shadow(color: isFormValid ? .blue.opacity(0.3) : .clear, radius: 5, x: 0, y: 2)
            }
            .disabled(!isFormValid || authService.isLoading)

            // Toggle Sign Up/In
            Button(action: { showSignUp.toggle() }) {
                Text(showSignUp ? "Already have an account? Sign In" : "Don't have an account? Sign Up")
                    .font(.body)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Modern Privacy Section

    private var modernPrivacySection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "shield.checkered")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.green)

                Text("Your privacy is protected")
                    .font(.system(size: 14, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
            }

            HStack(spacing: 20) {
                Button("Privacy Policy") {
                    showPrivacyPolicy = true
                }
                .font(.system(size: 12, weight: .medium, design: .rounded))
                .foregroundColor(.purple)

                Text("•")
                    .foregroundColor(.secondary)

                Button("Terms of Service") {
                    showTermsOfService = true
                }
                .font(.system(size: 12, weight: .medium, design: .rounded))
                .foregroundColor(.purple)
            }
        }
        .padding(.horizontal, 20)
    }

    private var modernDevelopmentSkipSection: some View {
        MagicButton(
            title: "Skip for Development",
            icon: "hammer.fill",
            style: .tertiary,
            action: skipAuthentication
        )
        .padding(.horizontal, 20)
    }

    // MARK: - Privacy Section (Legacy)

    private var privacySection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                Button("Privacy Policy") {
                    showPrivacyPolicy = true
                }
                .font(.caption)
                .foregroundColor(.blue)

                Text("•")
                    .foregroundColor(.secondary)

                Button("Terms of Service") {
                    showTermsOfService = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            Text("By continuing, you agree to our Terms of Service and Privacy Policy")
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
    }

    // MARK: - Development Skip Section

    private var developmentSkipSection: some View {
        VStack(spacing: 8) {
            Divider()
                .padding(.horizontal, 40)

            Button("Skip Authentication (Development Mode)") {
                // Skip authentication for development
                skipAuthentication()
            }
            .font(.caption)
            .foregroundColor(.orange)
            .padding(.bottom, 20)
        }
    }

    // MARK: - Computed Properties

    private var isFormValid: Bool {
        if showSignUp {
            return !email.isEmpty && !password.isEmpty && !fullName.isEmpty &&
                   email.contains("@") && password.count >= 6
        } else {
            return !email.isEmpty && !password.isEmpty && email.contains("@")
        }
    }

    // MARK: - Actions

    private func authenticateWithBiometrics() {
        Task {
            await authService.authenticateWithBiometrics()
        }
    }

    private func performEmailAuthentication() {
        Task {
            if showSignUp {
                await authService.signUpWithEmail(email, password: password, fullName: fullName)
            } else {
                await authService.signInWithEmail(email, password: password)
            }
        }
    }

    private func skipAuthentication() {
        print("🔧 Skip authentication button tapped")

        // Create a mock user for development
        let mockUser = User(
            id: "dev-user-123",
            email: "<EMAIL>",
            displayName: "Development User",
            subscriptionTier: .premium
        )

        // Enable development mode with mock user
        Task { @MainActor in
            print("🔧 About to enable development mode...")
            authService.enableDevelopmentMode(with: mockUser)
            print("🔧 Development mode enabled. isAuthenticated: \(authService.isAuthenticated)")
        }
    }
}

// MARK: - Custom Text Field Components

struct CustomTextField: View {
    let title: String
    @Binding var text: String
    let icon: String
    var keyboardType: UIKeyboardType = .default

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 20)

            TextField(title, text: $text)
                .keyboardType(keyboardType)
                .autocapitalization(keyboardType == .emailAddress ? .none : .words)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct CustomSecureField: View {
    let title: String
    @Binding var text: String
    let icon: String
    @State private var isSecure = true

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 20)

            if isSecure {
                SecureField(title, text: $text)
            } else {
                TextField(title, text: $text)
            }

            Button(action: { isSecure.toggle() }) {
                Image(systemName: isSecure ? "eye.slash" : "eye")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Supporting Views

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authService: AuthenticationService
    @State private var email = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Reset Password")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Enter your email address and we'll send you a link to reset your password.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                CustomTextField(
                    title: "Email",
                    text: $email,
                    icon: "envelope.fill",
                    keyboardType: .emailAddress
                )

                Button("Send Reset Link") {
                    Task {
                        await authService.resetPassword(email: email)
                        dismiss()
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(email.contains("@") ? Color.blue : Color.gray.opacity(0.3))
                .foregroundColor(.white)
                .cornerRadius(12)
                .disabled(!email.contains("@"))

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SimplePrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Privacy Policy")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("Your privacy is important to us. This privacy policy explains how PetTime Capsule collects, uses, and protects your information.")
                        .font(.body)

                    // Add more privacy policy content here

                    Spacer()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SimpleTermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Terms of Service")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("By using PetTime Capsule, you agree to these terms and conditions.")
                        .font(.body)

                    // Add more terms of service content here

                    Spacer()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AuthenticationView()
}
