//
//  MemoryVaultView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MemoryVaultView: View {
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var realDataService: RealDataService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @StateObject private var productionMemoryService = ProductionMemoryService()
    @StateObject private var advancedMemoryService = AdvancedMemoryService.shared
    @StateObject private var socialMemoryService = SocialMemoryService.shared
    @StateObject private var physicalProductsService = PhysicalProductsService.shared
    @StateObject private var vaultService = SecureVaultService.shared
    @State private var selectedTab = 0
    @State private var showAddMemory = false
    @State private var showCreateVault = false
    @State private var showPhysicalProducts = false
    @State private var showFamilySharing = false
    @State private var showAIMontage = false
    @State private var searchText = ""
    @State private var selectedFilter: MemoryFilter = .all
    @State private var animateCards = false
    @State private var showPremiumUpgrade = false
    @State private var showErrorAlert = false
    @State private var showNetworkError = false
    @State private var isOfflineMode = false
    @State private var lastFailedOperation: (() -> Void)?

    enum MemoryFilter: String, CaseIterable {
        case all = "All"
        case photos = "Photos"
        case videos = "Videos"
        case milestones = "Milestones"
        case favorites = "Favorites"
    }

    // MARK: - Computed Properties

    private var filteredMemories: [Memory] {
        var memories = productionMemoryService.memories

        // Apply filter
        switch selectedFilter {
        case .all:
            break
        case .photos:
            memories = memories.filter { $0.type == .photo }
        case .videos:
            memories = memories.filter { $0.type == .video }
        case .milestones:
            memories = memories.filter { $0.type == .milestone || $0.milestone != nil }
        case .favorites:
            // TODO: Add favorite property to Memory model
            break
        }

        // Apply search
        if !searchText.isEmpty {
            memories = memories.filter { memory in
                memory.title.localizedCaseInsensitiveContains(searchText) ||
                memory.content.localizedCaseInsensitiveContains(searchText) ||
                memory.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }

        return memories
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    memoriesTab
                        .tag(0)

                    vaultsTab
                        .tag(1)

                    aiCuratedTab
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Memories & Vault")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showFamilySharing = true }) {
                        Image(systemName: "person.2.fill")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        // Basic Features
                        Button(action: { showAddMemory = true }) {
                            Label("Add Memory", systemImage: "photo.badge.plus")
                        }

                        Button(action: { showCreateVault = true }) {
                            Label("Create Vault", systemImage: "archivebox.badge.plus")
                        }

                        Divider()

                        // AI Features (Premium)
                        Button(action: {
                            if subscriptionService.subscriptionStatus == .free {
                                showPremiumUpgrade = true
                            } else {
                                showAIMontage = true
                            }
                        }) {
                            Label("AI Video Montage", systemImage: "video.badge.waveform")
                        }

                        Button(action: {
                            if subscriptionService.subscriptionStatus == .free {
                                showPremiumUpgrade = true
                            } else {
                                Task {
                                    try await advancedMemoryService.performAdvancedAICuration(for: [])
                                }
                            }
                        }) {
                            Label("AI Memory Curation", systemImage: "brain.head.profile")
                        }

                        Divider()

                        // Physical Products (Premium)
                        Button(action: {
                            if subscriptionService.subscriptionStatus == .free {
                                showPremiumUpgrade = true
                            } else {
                                showPhysicalProducts = true
                            }
                        }) {
                            Label("Photo Books & Prints", systemImage: "book.fill")
                        }

                        // Family Sharing
                        Button(action: { showFamilySharing = true }) {
                            Label("Family Sharing", systemImage: "person.2.fill")
                        }

                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showAddMemory) {
                AddMemoryView()
                    .environmentObject(mockDataService)
                    .environmentObject(realDataService)
                    .environmentObject(productionMemoryService)
            }
            .sheet(isPresented: $showCreateVault) {
                CreateVaultView()
                    .environmentObject(SecureVaultService.shared)
            }
            .sheet(isPresented: $showPhysicalProducts) {
                PhysicalProductsView()
                    .environmentObject(physicalProductsService)
                    .environmentObject(subscriptionService)
            }
            .sheet(isPresented: $showFamilySharing) {
                FamilySharingView()
                    .environmentObject(socialMemoryService)
                    .environmentObject(subscriptionService)
            }
            .sheet(isPresented: $showAIMontage) {
                AIMontageCreatorView()
                    .environmentObject(advancedMemoryService)
                    .environmentObject(subscriptionService)
            }
            .sheet(isPresented: $showPremiumUpgrade) {
                PremiumUpgradeView(feature: "Advanced Memory Features")
                    .environmentObject(subscriptionService)
            }
            .alert("Error", isPresented: $showErrorAlert) {
                Button("OK") { }
            } message: {
                Text(productionMemoryService.error?.localizedDescription ?? "An unknown error occurred")
            }
            .overlay {
                if productionMemoryService.isUploading {
                    uploadProgressOverlay
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }

                // Load production memories
                productionMemoryService.loadMemories()
            }
            .onChange(of: productionMemoryService.error) { _, error in
                if error != nil {
                    showErrorAlert = true
                }
            }
            .alert("Error", isPresented: $showErrorAlert) {
                Button("Retry") {
                    productionMemoryService.loadMemories()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text(productionMemoryService.error?.localizedDescription ?? "An unknown error occurred")
            }
            .alert("Network Error", isPresented: $showNetworkError) {
                Button("Retry") {
                    retryFailedOperation()
                }
                Button("Offline Mode") {
                    enableOfflineMode()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("Unable to connect to the server. You can continue in offline mode or retry when connection is restored.")
            }
        }
    }

    // MARK: - Upload Progress Overlay

    private var uploadProgressOverlay: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                ProgressView(value: productionMemoryService.uploadProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .frame(width: 200)

                Text(productionMemoryService.processingStatus)
                    .font(.petSubheadline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                Text("\(Int(productionMemoryService.uploadProgress * 100))%")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
            )
        }
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Memories", icon: "photo.fill", index: 0)
            tabButton(title: "Vaults", icon: "archivebox.fill", index: 1)
            tabButton(title: "AI Curated", icon: "brain.head.profile", index: 2)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)

                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Memories Tab

    private var memoriesTab: some View {
        VStack(spacing: 0) {
            // Search and Filter
            searchAndFilterSection

            // Memories Grid
            if productionMemoryService.isLoading {
                loadingMemoriesView
            } else if productionMemoryService.memories.isEmpty {
                emptyMemoriesView
            } else {
                memoriesGridView
            }
        }
    }

    private var searchAndFilterSection: some View {
        VStack(spacing: 16) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search memories...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )

            // Filter Chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(MemoryFilter.allCases, id: \.self) { filter in
                        filterChip(filter: filter)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private func filterChip(filter: MemoryFilter) -> some View {
        Button(action: { selectedFilter = filter }) {
            Text(filter.rawValue)
                .font(.petSubheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(selectedFilter == filter ? Color.purple : Color(.systemGray6))
                )
                .foregroundColor(selectedFilter == filter ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var memoriesGridView: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(filteredMemories.enumerated()), id: \.element.id) { index, memory in
                    memoryCard(memory: memory)
                        .scaleEffect(animateCards ? 1.0 : 0.9)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.05), value: animateCards)
                        .onAppear {
                            // Preload next batch when approaching end
                            if memory == filteredMemories.last {
                                loadMoreMemoriesIfNeeded()
                            }
                        }
                }

                // Loading indicator for pagination
                if productionMemoryService.isLoading && !filteredMemories.isEmpty {
                    VStack {
                        ProgressView()
                            .tint(.purple)
                        Text("Loading more memories...")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .gridCellColumns(2)
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
        .refreshable {
            productionMemoryService.loadMemories()
        }
    }

    // MARK: - Performance Optimization Methods

    private func loadMoreMemoriesIfNeeded() {
        // Implement pagination logic
        guard !productionMemoryService.isLoading else { return }

        Task {
            // Load next batch of memories
            await productionMemoryService.loadMoreMemories()
        }
    }

    private func memoryCard(memory: Memory) -> some View {
        VStack(spacing: 0) {
            // Memory Image/Video
            ZStack {
                if let mediaURL = memory.mediaURL, let url = URL(string: mediaURL) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .overlay(
                                ProgressView()
                                    .tint(.white)
                            )
                    }
                    .frame(height: 150)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                } else {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .aspectRatio(1, contentMode: .fit)
                        .overlay(
                            VStack {
                                Image(systemName: memory.type.systemImage)
                                    .font(.title)
                                    .foregroundColor(.white)

                                Text(memory.type.displayName)
                                    .font(.petCaption)
                                    .foregroundColor(.white)
                            }
                        )
                }

                // Video duration overlay
                if memory.type == .video, let duration = memory.formattedDuration {
                    VStack {
                        HStack {
                            Spacer()
                            Text(duration)
                                .font(.caption2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.black.opacity(0.7))
                                )
                                .padding(8)
                        }
                        Spacer()
                    }
                }

                // AI Analysis indicator
                if !memory.tags.isEmpty || memory.milestone != nil {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "brain.head.profile")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.purple.opacity(0.8))
                                )
                                .padding(8)
                        }
                    }
                }
            }

            // Memory Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(memory.title)
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Spacer()

                    if let pet = memory.pet {
                        Text(pet.name)
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }

                Text(timeAgoString(from: memory.createdAt))
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                // AI Analysis tags
                if !memory.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 4) {
                            ForEach(memory.tags.prefix(3), id: \.self) { tag in
                                Text(tag)
                                    .font(.caption2)
                                    .foregroundColor(.purple)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(Color.purple.opacity(0.1))
                                    )
                            }
                        }
                    }
                }

                // Milestone indicator
                if let milestone = memory.milestone {
                    HStack {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)

                        Text(milestone)
                            .font(.caption2)
                            .foregroundColor(.orange)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
            .padding(12)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onTapGesture {
            // Navigate to memory detail
        }
    }

    // MARK: - Helper Methods

    private func timeAgoString(from date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    private var loadingMemoriesView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.purple)

            Text("Loading memories...")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }

    private var emptyMemoriesView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "photo.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.purple.opacity(0.6))

            VStack(spacing: 8) {
                Text("No memories yet")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Start capturing precious moments with your pets")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: { showAddMemory = true }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add First Memory")
                }
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )
            }

            Spacer()
        }
        .padding()
    }

    // MARK: - Vaults Tab

    private var vaultsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if vaultService.vaults.isEmpty {
                    emptyVaultsView
                } else {
                    ForEach(Array(vaultService.vaults.enumerated()), id: \.element.id) { index, vault in
                        realVaultCard(vault: vault)
                            .scaleEffect(animateCards ? 1.0 : 0.9)
                            .opacity(animateCards ? 1.0 : 0.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                    }
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func realVaultCard(vault: SecureVault) -> some View {
        HStack(spacing: 16) {
            // Vault Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [.purple, .blue]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)

                Image(systemName: vault.isLocked ? "lock.fill" : "lock.open.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }

            // Vault Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(vault.name)
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    if vault.isLocked {
                        Text("LOCKED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.red)
                            )
                    } else {
                        Text("UNLOCKED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.green)
                            )
                    }
                }

                Text("\(vault.memoryCount) memories • Created \(formatDate(vault.createdAt))")
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text(vault.description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            // Access Button
            Button(action: {
                // TODO: Show unlock vault view
            }) {
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    private func vaultCard(index: Int) -> some View {
        HStack(spacing: 16) {
            // Vault Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.orange.opacity(0.3), .red.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)

                Image(systemName: "archivebox.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }

            // Vault Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Vault \(index + 1)")
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    if index % 3 == 0 {
                        Text("LOCKED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.orange)
                            )
                    }
                }

                Text("\(Int.random(in: 5...50)) memories • Created \(Int.random(in: 1...365)) days ago")
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text("A collection of special moments from \(mockDataService.mockPets.randomElement()?.name ?? "Pet")")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            // Chevron
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onTapGesture {
            // Navigate to vault detail
        }
    }

    private var emptyVaultsView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "archivebox.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.orange.opacity(0.6))

            VStack(spacing: 8) {
                Text("No vaults created")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Create time capsules to preserve special moments")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: { showCreateVault = true }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Create First Vault")
                }
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange)
                )
            }

            Spacer()
        }
        .padding()
    }

    // MARK: - AI Curated Tab

    private var aiCuratedTab: some View {
        ScrollView {
            LazyVStack(spacing: 24) {
                // Premium Feature Banner - Removed for development
                // if subscriptionService.subscriptionStatus == .free {
                //     premiumFeatureBanner
                // }

                // AI Processing Status
                if advancedMemoryService.isProcessing {
                    aiProcessingView
                }

                // AI Insights Section
                if !advancedMemoryService.aiInsights.isEmpty {
                    aiInsightsSection
                }

                // Smart Collections
                smartCollectionsSection

                // Montage Templates
                montageTemplatesSection

                // Milestone Detection
                milestonesSection

                // AI Suggestions Header
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "brain.head.profile.fill")
                            .font(.title2)
                            .foregroundColor(.purple)

                        Text("AI Curated Collections")
                            .font(.petTitle2)
                            .fontWeight(.bold)

                        Spacer()

                        Button("Refresh AI Analysis") {
                            Task {
                                try await advancedMemoryService.performAdvancedAICuration(for: [])
                            }
                        }
                        .font(.caption)
                        .foregroundColor(.purple)
                    }

                    Text("Our AI has analyzed your memories and created these special collections")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }
                .padding()

                // AI Collections
                ForEach(0..<4, id: \.self) { index in
                    aiCollectionCard(index: index)
                        .scaleEffect(animateCards ? 1.0 : 0.9)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func aiCollectionCard(index: Int) -> some View {
        let collections = [
            ("Happy Moments", "brain.head.profile.fill", "12 photos of pure joy", Color.yellow),
            ("Growth Journey", "arrow.up.circle.fill", "8 videos showing development", Color.green),
            ("Playful Times", "gamecontroller.fill", "15 memories of fun activities", Color.blue),
            ("Peaceful Moments", "moon.fill", "6 photos of relaxation", Color.purple)
        ]

        let collection = collections[index]

        return VStack(spacing: 0) {
            // Collection Preview
            HStack(spacing: 8) {
                ForEach(0..<3, id: \.self) { photoIndex in
                    RoundedRectangle(cornerRadius: 8)
                        .fill(collection.3.opacity(0.3))
                        .aspectRatio(1, contentMode: .fit)
                        .overlay(
                            Image(systemName: "photo.fill")
                                .font(.caption)
                                .foregroundColor(.white)
                        )
                }
            }
            .padding()

            // Collection Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: collection.1)
                        .font(.title3)
                        .foregroundColor(collection.3)

                    Text(collection.0)
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    Text("AI")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.purple)
                        )
                }

                Text(collection.2)
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Button("View Collection") {
                    // Navigate to AI collection
                }
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(collection.3)
            }
            .padding()
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - New AI Sections

    private var premiumFeatureBanner: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "crown.fill")
                    .font(.title2)
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Unlock AI Memory Features")
                        .font(.headline)
                        .fontWeight(.bold)

                    Text("Advanced AI curation, smart collections, and video montages")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            Button("Upgrade to Premium") {
                showPremiumUpgrade = true
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                LinearGradient(
                    colors: [.purple, .blue],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .padding(.horizontal)
    }

    private var aiProcessingView: some View {
        VStack(spacing: 16) {
            ProgressView(value: advancedMemoryService.processingProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .purple))

            Text(advancedMemoryService.currentOperation)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .padding(.horizontal)
    }

    private var aiInsightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("AI Insights")
                .font(.headline)
                .fontWeight(.bold)

            LazyVStack(spacing: 12) {
                ForEach(advancedMemoryService.aiInsights) { insight in
                    HStack(spacing: 12) {
                        Image(systemName: insight.type == .milestone ? "star.fill" : "lightbulb.fill")
                            .font(.title3)
                            .foregroundColor(.purple)

                        VStack(alignment: .leading, spacing: 4) {
                            Text(insight.title)
                                .font(.subheadline)
                                .fontWeight(.semibold)

                            Text(insight.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        if insight.actionable {
                            Button("Act") {
                                // Handle insight action
                            }
                            .font(.caption)
                            .foregroundColor(.purple)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }
            }
        }
        .padding(.horizontal)
    }

    private var smartCollectionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Smart Collections")
                .font(.headline)
                .fontWeight(.bold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(0..<3) { index in
                        smartCollectionCard(index: index)
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private func smartCollectionCard(index: Int) -> some View {
        VStack(spacing: 12) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)

                Image(systemName: ["heart.fill", "star.fill", "camera.fill"][index])
                    .font(.title)
                    .foregroundColor(.white)
            }

            VStack(spacing: 4) {
                Text(["Happy Moments", "Milestones", "Adventures"][index])
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text("\(Int.random(in: 5...25)) memories")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(width: 140)
    }

    private var montageTemplatesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Video Montage Templates")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                if subscriptionService.subscriptionStatus != .free {
                    Button("Create Montage") {
                        showAIMontage = true
                    }
                    .font(.caption)
                    .foregroundColor(.purple)
                }
            }

            LazyVStack(spacing: 12) {
                ForEach(advancedMemoryService.suggestedMontages) { template in
                    montageTemplateCard(template)
                }
            }
        }
        .padding(.horizontal)
    }

    private func montageTemplateCard(_ template: MontageTemplate) -> some View {
        HStack(spacing: 16) {
            Image(systemName: template.thumbnail)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 50, height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple.opacity(0.1))
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(template.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(template.description)
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack {
                    Text("\(Int(template.duration))s")
                        .font(.caption2)
                        .foregroundColor(.purple)

                    if template.isPremium {
                        Image(systemName: "crown.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)
                    }
                }
            }

            Spacer()

            Button("Create") {
                if template.isPremium && subscriptionService.subscriptionStatus == .free {
                    showPremiumUpgrade = true
                } else {
                    showAIMontage = true
                }
            }
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.purple)
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    private var milestonesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Detected Milestones")
                .font(.headline)
                .fontWeight(.bold)

            LazyVStack(spacing: 12) {
                ForEach(advancedMemoryService.milestoneDetections) { milestone in
                    milestoneCard(milestone)
                }
            }
        }
        .padding(.horizontal)
    }

    private func milestoneCard(_ milestone: MilestoneDetection) -> some View {
        HStack(spacing: 16) {
            Image(systemName: "star.fill")
                .font(.title2)
                .foregroundColor(.yellow)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.yellow.opacity(0.1))
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(milestone.milestoneType.replacingOccurrences(of: "_", with: " ").capitalized)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(milestone.description)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(milestone.detectedAt.formatted(date: .abbreviated, time: .omitted))
                    .font(.caption2)
                    .foregroundColor(.purple)
            }

            Spacer()

            VStack {
                Text("\(Int(milestone.confidence * 10))/10")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)

                Text("Confidence")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Error Handling Methods

    private func retryFailedOperation() {
        lastFailedOperation?()
    }

    private func enableOfflineMode() {
        isOfflineMode = true
        showNetworkError = false
        // TODO: Implement offline mode functionality
    }

    private func handleNetworkError(operation: @escaping () -> Void) {
        lastFailedOperation = operation
        showNetworkError = true
    }
}
