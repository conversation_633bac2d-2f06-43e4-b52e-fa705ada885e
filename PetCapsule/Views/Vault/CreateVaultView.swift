//
//  CreateVaultView.swift
//  PetCapsule
//
//  Create secure vault with password protection
//

import SwiftUI

struct CreateVaultView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var vaultService = SecureVaultService.shared
    @State private var vaultName = ""
    @State private var vaultDescription = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var securityQuestion = ""
    @State private var securityAnswer = ""
    @State private var showPassword = false
    @State private var isCreating = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    private let securityQuestions = [
        "What was the name of your first pet?",
        "What city were you born in?",
        "What is your mother's maiden name?",
        "What was the name of your elementary school?",
        "What is your favorite movie?",
        "Custom question..."
    ]
    
    @State private var selectedQuestionIndex = 0
    @State private var customQuestion = ""
    
    var isFormValid: Bool {
        !vaultName.isEmpty &&
        !vaultDescription.isEmpty &&
        password.count >= 6 &&
        password == confirmPassword &&
        !finalSecurityQuestion.isEmpty &&
        !securityAnswer.isEmpty
    }
    
    var finalSecurityQuestion: String {
        if selectedQuestionIndex == securityQuestions.count - 1 {
            return customQuestion
        } else {
            return securityQuestions[selectedQuestionIndex]
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "lock.shield.fill")
                            .font(.system(size: 48))
                            .foregroundColor(.purple)
                        
                        Text("Create Secure Vault")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Create a password-protected vault to store your most precious memories securely")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top)
                    
                    // Vault Details
                    VStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Vault Name")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextField("Enter vault name", text: $vaultName)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Description")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextField("Describe what you'll store in this vault", text: $vaultDescription, axis: .vertical)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .lineLimit(3...6)
                        }
                    }
                    
                    // Password Section
                    VStack(spacing: 16) {
                        Text("Security Settings")
                            .font(.title3)
                            .fontWeight(.semibold)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Password")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            HStack {
                                if showPassword {
                                    TextField("Enter password (min 6 characters)", text: $password)
                                } else {
                                    SecureField("Enter password (min 6 characters)", text: $password)
                                }
                                
                                Button(action: { showPassword.toggle() }) {
                                    Image(systemName: showPassword ? "eye.slash" : "eye")
                                        .foregroundColor(.secondary)
                                }
                            }
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            if !password.isEmpty && password.count < 6 {
                                Text("Password must be at least 6 characters")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Confirm Password")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            SecureField("Confirm password", text: $confirmPassword)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            if !confirmPassword.isEmpty && password != confirmPassword {
                                Text("Passwords do not match")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    
                    // Security Question Section
                    VStack(spacing: 16) {
                        Text("Recovery Question")
                            .font(.title3)
                            .fontWeight(.semibold)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Security Question")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Picker("Security Question", selection: $selectedQuestionIndex) {
                                ForEach(0..<securityQuestions.count, id: \.self) { index in
                                    Text(securityQuestions[index])
                                        .tag(index)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                            .frame(maxWidth: .infinity, alignment: .leading)
                            
                            if selectedQuestionIndex == securityQuestions.count - 1 {
                                TextField("Enter your custom question", text: $customQuestion)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Security Answer")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextField("Enter your answer", text: $securityAnswer)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            Text("This answer will help you recover access if you forget your password")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Create Button
                    Button(action: createVault) {
                        HStack {
                            if isCreating {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            }
                            Text("Create Secure Vault")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isFormValid ? Color.purple : Color.gray.opacity(0.3))
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(!isFormValid || isCreating)
                    
                    // Security Notice
                    VStack(spacing: 8) {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text("Important Security Notice")
                                .font(.headline)
                                .foregroundColor(.orange)
                        }
                        
                        Text("Please remember your password and security answer. We cannot recover your vault if you forget both. Your memories will be encrypted and only accessible with the correct credentials.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("New Vault")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Error", isPresented: $showError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private func createVault() {
        isCreating = true
        
        Task {
            do {
                let userId = UUID(uuidString: "dev-user-123") ?? UUID() // Development mode
                
                _ = try await vaultService.createVault(
                    name: vaultName,
                    description: vaultDescription,
                    password: password,
                    securityQuestion: finalSecurityQuestion,
                    securityAnswer: securityAnswer,
                    userId: userId
                )
                
                await MainActor.run {
                    isCreating = false
                    dismiss()
                }
                
            } catch {
                await MainActor.run {
                    isCreating = false
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }
}

#Preview {
    CreateVaultView()
}
