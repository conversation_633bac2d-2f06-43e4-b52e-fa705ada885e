//
//  NutritionGuideView.swift
//  PetCapsule
//
//  Pet nutrition guides and feeding schedules
//

import SwiftUI

struct NutritionGuideView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "leaf.fill")
                            .font(.system(size: 64))
                            .foregroundColor(.green)
                        
                        Text("Nutrition Guide")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Feeding schedules, diet plans, and nutrition tips for optimal pet health")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 40)
                    }
                    .padding(.top, 40)
                    
                    // Coming Soon
                    VStack(spacing: 16) {
                        Text("🚧 Coming Soon")
                            .font(.title3)
                            .fontWeight(.semibold)
                        
                        Text("We're developing comprehensive nutrition resources including:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            featureItem("Age-appropriate feeding schedules")
                            featureItem("Breed-specific diet recommendations")
                            featureItem("Weight management plans")
                            featureItem("Special dietary needs")
                            featureItem("Treat and supplement guides")
                            featureItem("Food allergy management")
                            featureItem("Nutritionist consultations")
                        }
                        .padding()
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(12)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Nutrition Guide")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func featureItem(_ text: String) -> some View {
        HStack {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
            Text(text)
                .font(.subheadline)
            Spacer()
        }
    }
}

#Preview {
    NutritionGuideView()
}
