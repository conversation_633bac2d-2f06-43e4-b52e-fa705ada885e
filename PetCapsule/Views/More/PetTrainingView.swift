//
//  PetTrainingView.swift
//  PetCapsule
//
//  Pet training resources and guides
//

import SwiftUI

struct PetTrainingView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "graduationcap.fill")
                            .font(.system(size: 64))
                            .foregroundColor(.orange)
                        
                        Text("Pet Training Resources")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Professional training guides and tips for your pet")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 40)
                    }
                    .padding(.top, 40)
                    
                    // Coming Soon
                    VStack(spacing: 16) {
                        Text("🚧 Coming Soon")
                            .font(.title3)
                            .fontWeight(.semibold)
                        
                        Text("We're working on comprehensive training resources including:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            featureItem("Basic obedience training")
                            featureItem("House training guides")
                            featureItem("Behavioral modification")
                            featureItem("Socialization tips")
                            featureItem("Advanced tricks and commands")
                            featureItem("Professional trainer directory")
                        }
                        .padding()
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(12)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Pet Training")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func featureItem(_ text: String) -> some View {
        HStack {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.orange)
            Text(text)
                .font(.subheadline)
            Spacer()
        }
    }
}

#Preview {
    PetTrainingView()
}
