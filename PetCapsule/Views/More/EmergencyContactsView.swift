//
//  EmergencyContactsView.swift
//  PetCapsule
//
//  Emergency contacts for pet care
//

import SwiftUI

struct EmergencyContactsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var emergencyService = EmergencyContactsService.shared
    @State private var showAddContact = false
    @State private var animateItems = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Quick Emergency Numbers
                    quickEmergencySection
                    
                    // Poison Control
                    poisonControlSection
                    
                    // Your Saved Contacts
                    savedContactsSection
                }
                .padding()
            }
            .navigationTitle("Emergency Contacts")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Contact") {
                        showAddContact = true
                    }
                    .foregroundColor(.red)
                }
            }
            .sheet(isPresented: $showAddContact) {
                AddEmergencyContactView()
                    .environmentObject(emergencyService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateItems = true
                }
            }
        }
    }
    
    // MARK: - Quick Emergency Section
    
    private var quickEmergencySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🚨 Quick Emergency")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                emergencyContactCard(
                    title: "ASPCA Poison Control",
                    subtitle: "24/7 Animal Poison Control",
                    phone: "(*************",
                    color: .red,
                    isEmergency: true
                )
                
                emergencyContactCard(
                    title: "Pet Poison Helpline",
                    subtitle: "24/7 Pet Poison Emergency",
                    phone: "(*************",
                    color: .orange,
                    isEmergency: true
                )
                
                emergencyContactCard(
                    title: "Emergency Vet Locator",
                    subtitle: "Find nearest emergency vet",
                    phone: "Search",
                    color: .blue,
                    isEmergency: false,
                    action: {
                        // Open vet search
                    }
                )
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateItems)
    }
    
    // MARK: - Poison Control Section
    
    private var poisonControlSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("☠️ Poison Control Information")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("Common Pet Toxins")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 8) {
                    toxinItem("🍫 Chocolate", "Especially dark chocolate")
                    toxinItem("🧅 Onions & Garlic", "Can cause anemia")
                    toxinItem("🍇 Grapes & Raisins", "Can cause kidney failure")
                    toxinItem("🥜 Xylitol (Sugar substitute)", "Found in gum and candy")
                    toxinItem("💊 Human Medications", "Especially pain relievers")
                    toxinItem("🌸 Certain Plants", "Lilies, azaleas, tulips")
                }
                
                Text("If your pet ingests any toxic substance, call poison control immediately!")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateItems)
    }
    
    // MARK: - Saved Contacts Section
    
    private var savedContactsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("📞 Your Emergency Contacts")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Add") {
                    showAddContact = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            if emergencyService.contacts.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "phone.badge.plus")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)
                    
                    Text("No emergency contacts saved")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Add your vet and other important contacts for quick access during emergencies")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                    
                    Button("Add First Contact") {
                        showAddContact = true
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                VStack(spacing: 12) {
                    ForEach(Array(emergencyService.contacts.enumerated()), id: \.element.id) { index, contact in
                        savedContactCard(contact: contact)
                            .scaleEffect(animateItems ? 1.0 : 0.9)
                            .opacity(animateItems ? 1.0 : 0.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3 + Double(index) * 0.1), value: animateItems)
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Views
    
    private func emergencyContactCard(
        title: String,
        subtitle: String,
        phone: String,
        color: Color,
        isEmergency: Bool,
        action: (() -> Void)? = nil
    ) -> some View {
        HStack(spacing: 16) {
            // Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.15))
                    .frame(width: 50, height: 50)
                
                Image(systemName: isEmergency ? "phone.fill" : "magnifyingglass")
                    .font(.title3)
                    .foregroundColor(color)
            }
            
            // Info
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Action Button
            Button(action: {
                if let action = action {
                    action()
                } else if isEmergency {
                    callNumber(phone)
                }
            }) {
                Text(isEmergency ? "Call" : phone)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(color)
                    .cornerRadius(20)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
    
    private func toxinItem(_ name: String, _ description: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Text("•")
                .foregroundColor(.red)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
    
    private func savedContactCard(contact: EmergencyContact) -> some View {
        HStack(spacing: 16) {
            // Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(contact.type.color.opacity(0.15))
                    .frame(width: 50, height: 50)
                
                Image(systemName: contact.type.icon)
                    .font(.title3)
                    .foregroundColor(contact.type.color)
            }
            
            // Info
            VStack(alignment: .leading, spacing: 4) {
                Text(contact.name)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text(contact.type.displayName)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if !contact.notes.isEmpty {
                    Text(contact.notes)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
            
            Spacer()
            
            // Call Button
            Button(action: { callNumber(contact.phoneNumber) }) {
                Image(systemName: "phone.fill")
                    .font(.title3)
                    .foregroundColor(.green)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button("Delete") {
                emergencyService.deleteContact(contact)
            }
            .tint(.red)
        }
    }
    
    private func callNumber(_ number: String) {
        let cleanNumber = number.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        if let url = URL(string: "tel:\(cleanNumber)") {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - Add Emergency Contact View

struct AddEmergencyContactView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var emergencyService: EmergencyContactsService
    @State private var name = ""
    @State private var phoneNumber = ""
    @State private var notes = ""
    @State private var selectedType = EmergencyContactType.veterinarian
    @State private var isAdding = false
    
    var isFormValid: Bool {
        !name.isEmpty && !phoneNumber.isEmpty
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    VStack(spacing: 20) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Contact Name")
                                .font(.headline)
                            
                            TextField("Enter contact name", text: $name)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Phone Number")
                                .font(.headline)
                            
                            TextField("Enter phone number", text: $phoneNumber)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .keyboardType(.phonePad)
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Contact Type")
                                .font(.headline)
                            
                            Picker("Contact Type", selection: $selectedType) {
                                ForEach(EmergencyContactType.allCases, id: \.self) { type in
                                    HStack {
                                        Image(systemName: type.icon)
                                        Text(type.displayName)
                                    }
                                    .tag(type)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Notes (Optional)")
                                .font(.headline)
                            
                            TextField("Additional information", text: $notes, axis: .vertical)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .lineLimit(3...6)
                        }
                    }
                    
                    Button(action: addContact) {
                        HStack {
                            if isAdding {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            }
                            Text("Add Emergency Contact")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isFormValid ? Color.red : Color.gray.opacity(0.3))
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(!isFormValid || isAdding)
                }
                .padding()
            }
            .navigationTitle("Add Emergency Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func addContact() {
        isAdding = true
        
        Task {
            await emergencyService.addContact(
                name: name,
                phoneNumber: phoneNumber,
                type: selectedType,
                notes: notes
            )
            
            await MainActor.run {
                isAdding = false
                dismiss()
            }
        }
    }
}

#Preview {
    EmergencyContactsView()
}
