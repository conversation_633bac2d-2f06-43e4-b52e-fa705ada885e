//
//  SubscriptionService.swift
//  PetCapsule
//
//  Premium subscription management for $2M/month revenue
//

import Foundation
import StoreKit
import SwiftUI

@MainActor
class SubscriptionService: ObservableObject {
    static let shared = SubscriptionService()

    @Published var subscriptionStatus: SubscriptionStatus = .free
    @Published var currentPlan: SubscriptionPlan?
    @Published var availablePlans: [SubscriptionPlan] = []
    @Published var isLoading = false
    @Published var revenue: RevenueMetrics = RevenueMetrics()

    private var updateListenerTask: Task<Void, Error>?

    init() {
        setupPlans()
        startListeningForTransactions()
    }

    deinit {
        updateListenerTask?.cancel()
    }

    // MARK: - Subscription Plans

    private func setupPlans() {
        availablePlans = [
            // Free Plan - Limited features
            SubscriptionPlan(
                id: "free",
                name: "Free",
                price: 0,
                duration: .lifetime,
                features: [
                    "5 memories per month",
                    "Basic photo storage",
                    "Standard timeline",
                    "Community access"
                ],
                limitations: [
                    "No AI features",
                    "No video montages",
                    "No premium themes",
                    "Limited storage (100MB)"
                ]
            ),

            // Premium Plan - Main revenue driver
            SubscriptionPlan(
                id: "premium_monthly",
                name: "Premium",
                price: 19.99,
                duration: .monthly,
                features: [
                    "Unlimited memories",
                    "AI memory curation",
                    "Professional video montages",
                    "Premium memorial themes",
                    "Advanced analytics",
                    "Priority support",
                    "10GB storage",
                    "Custom branding"
                ],
                aiFeatures: [
                    "Smart memory suggestions",
                    "Automatic milestone detection",
                    "Sentiment analysis",
                    "Behavioral insights",
                    "Personalized content"
                ]
            ),

            // Family Plan - Higher value
            SubscriptionPlan(
                id: "family_monthly",
                name: "Family",
                price: 39.99,
                duration: .monthly,
                features: [
                    "Everything in Premium",
                    "Up to 10 pets",
                    "Family sharing",
                    "Multi-user access",
                    "50GB storage",
                    "Advanced pet health tracking",
                    "Veterinarian integration",
                    "Emergency pet profiles"
                ],
                maxPets: 10,
                familyFeatures: true
            ),

            // Professional Plan - For breeders/vets
            SubscriptionPlan(
                id: "professional_monthly",
                name: "Professional",
                price: 99.99,
                duration: .monthly,
                features: [
                    "Everything in Family",
                    "Unlimited pets",
                    "Business tools",
                    "Client management",
                    "Breeding records",
                    "Health certificates",
                    "Custom branding",
                    "API access",
                    "Analytics dashboard",
                    "White-label options"
                ],
                businessFeatures: true,
                isProfessional: true
            ),

            // Lifetime Plan - High-value one-time purchase
            SubscriptionPlan(
                id: "lifetime",
                name: "Lifetime",
                price: 499.99,
                duration: .lifetime,
                features: [
                    "All Premium features forever",
                    "Future feature access",
                    "Priority support for life",
                    "Exclusive community access",
                    "Early beta access",
                    "Unlimited storage",
                    "Legacy protection guarantee"
                ],
                isLifetime: true,
                popularBadge: true
            )
        ]
    }

    // MARK: - Purchase Management

    func purchase(_ plan: SubscriptionPlan) async throws {
        isLoading = true
        defer { isLoading = false }

        // Simulate StoreKit purchase
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        // Update subscription status
        subscriptionStatus = .premium
        currentPlan = plan

        // Track revenue
        await trackRevenue(plan: plan, type: .subscription)

        // Unlock features
        await unlockPremiumFeatures()

        // Send to analytics
        await AnalyticsService.shared.trackPurchase(plan: plan)
    }

    func restorePurchases() async throws {
        isLoading = true
        defer { isLoading = false }

        // Simulate restore
        try await Task.sleep(nanoseconds: 1_000_000_000)

        // Check for existing purchases
        if let existingPlan = availablePlans.first(where: { $0.id == "premium_monthly" }) {
            subscriptionStatus = .premium
            currentPlan = existingPlan
            await unlockPremiumFeatures()
        }
    }

    // MARK: - Revenue Tracking

    private func trackRevenue(plan: SubscriptionPlan, type: RevenueType) async {
        let revenueEvent = RevenueEvent(
            amount: plan.price,
            plan: plan,
            type: type,
            timestamp: Date()
        )

        revenue.addEvent(revenueEvent)

        // Send to backend analytics
        await sendRevenueToBackend(revenueEvent)
    }

    private func sendRevenueToBackend(_ event: RevenueEvent) async {
        // Integration with analytics platforms
        // Mixpanel, Amplitude, custom backend
    }

    // MARK: - Feature Management

    private func unlockPremiumFeatures() async {
        // Unlock AI features
        // AIService.shared.unlockPremiumFeatures()

        // Unlock video features
        // VideoMontageService.shared.unlockPremiumFeatures()

        // Unlock storage
        // StorageService.shared.upgradeToPremium()

        // Unlock themes
        // ThemeService.shared.unlockPremiumThemes()
    }

    func hasFeature(_ feature: PremiumFeature) -> Bool {
        // Make all features available for now - remove premium restrictions
        return true

        // Original premium logic (commented out)
        // guard let plan = currentPlan else { return false }
        //
        // switch feature {
        // case .aiCuration:
        //     return plan.price > 0
        // case .videoMontages:
        //     return plan.price > 0
        // case .premiumThemes:
        //     return plan.price > 0
        // case .unlimitedStorage:
        //     return plan.isLifetime || plan.price >= 39.99
        // case .familySharing:
        //     return plan.familyFeatures
        // case .businessTools:
        //     return plan.businessFeatures
        // case .apiAccess:
        //     return plan.isProfessional
        // }
    }

    // MARK: - Transaction Listening

    private func startListeningForTransactions() {
        updateListenerTask = Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)
                    await self.updateSubscriptionStatus(transaction)
                    await transaction.finish()
                } catch {
                    print("Transaction verification failed: \(error)")
                }
            }
        }
    }

    private func checkVerified<T>(_ result: VerificationResult<T>) async throws -> T {
        switch result {
        case .unverified:
            throw SubscriptionError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    private func updateSubscriptionStatus(_ transaction: StoreKit.Transaction) async {
        // Update subscription based on transaction
        if let plan = availablePlans.first(where: { $0.id == transaction.productID }) {
            subscriptionStatus = .premium
            currentPlan = plan
            await unlockPremiumFeatures()
        }
    }
}

// MARK: - Data Models

struct SubscriptionPlan: Identifiable, Codable {
    let id: String
    let name: String
    let price: Double
    let duration: SubscriptionDuration
    let features: [String]
    var limitations: [String] = []
    var aiFeatures: [String] = []
    var maxPets: Int = 3
    var familyFeatures: Bool = false
    var businessFeatures: Bool = false
    var isProfessional: Bool = false
    var isLifetime: Bool = false
    var popularBadge: Bool = false

    var formattedPrice: String {
        if price == 0 {
            return "Free"
        } else if isLifetime {
            return "$\(Int(price)) once"
        } else {
            return String(format: "$%.2f/month", price)
        }
    }

    var monthlyRevenue: Double {
        switch duration {
        case .monthly:
            return price
        case .yearly:
            return price / 12
        case .lifetime:
            return price / 60 // Amortized over 5 years
        }
    }
}

enum SubscriptionDuration: String, Codable, CaseIterable {
    case monthly = "monthly"
    case yearly = "yearly"
    case lifetime = "lifetime"
}

enum SubscriptionStatus: String, Codable {
    case free = "free"
    case premium = "premium"
    case family = "family"
    case professional = "professional"
    case expired = "expired"
}

enum PremiumFeature: String, CaseIterable {
    case aiCuration = "ai_curation"
    case videoMontages = "video_montages"
    case premiumThemes = "premium_themes"
    case unlimitedStorage = "unlimited_storage"
    case familySharing = "family_sharing"
    case businessTools = "business_tools"
    case apiAccess = "api_access"
}

enum RevenueType: String, Codable {
    case subscription = "subscription"
    case oneTime = "one_time"
    case addon = "addon"
    case marketplace = "marketplace"
}

struct RevenueEvent: Identifiable, Codable {
    var id = UUID()
    let amount: Double
    let plan: SubscriptionPlan
    let type: RevenueType
    let timestamp: Date
}

@MainActor
class RevenueMetrics: ObservableObject {
    @Published var monthlyRevenue: Double = 0
    @Published var totalRevenue: Double = 0
    @Published var activeSubscribers: Int = 0
    @Published var churnRate: Double = 0
    @Published var averageRevenuePerUser: Double = 0
    @Published var revenueGrowth: Double = 0

    private var events: [RevenueEvent] = []

    func addEvent(_ event: RevenueEvent) {
        events.append(event)
        calculateMetrics()
    }

    private func calculateMetrics() {
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())

        // Monthly revenue
        monthlyRevenue = events
            .filter {
                Calendar.current.component(.month, from: $0.timestamp) == currentMonth &&
                Calendar.current.component(.year, from: $0.timestamp) == currentYear
            }
            .reduce(0) { $0 + $1.amount }

        // Total revenue
        totalRevenue = events.reduce(0) { $0 + $1.amount }

        // Active subscribers (mock)
        activeSubscribers = Int(monthlyRevenue / 20) // Assuming $20 average

        // ARPU
        if activeSubscribers > 0 {
            averageRevenuePerUser = monthlyRevenue / Double(activeSubscribers)
        }
    }
}

enum SubscriptionError: Error {
    case failedVerification
    case purchaseFailed
    case restoreFailed
    case networkError
}
