//
//  KnowledgeBaseService.swift
//  PetCapsule
//
//  Service for managing knowledge base folders and documents
//

import Foundation
import SwiftUI
import Supabase

@MainActor
class KnowledgeBaseService: ObservableObject {
    static let shared = KnowledgeBaseService()
    
    @Published var folders: [KnowledgeFolder] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabase = SupabaseClient(
        supabaseURL: URL(string: Config.Supabase.url)!,
        supabaseKey: Config.Supabase.anonKey
    )
    
    private init() {
        Task {
            await loadFolders()
        }
    }
    
    // MARK: - Folder Management
    
    func createFolder(
        name: String,
        description: String,
        icon: String,
        color: Color,
        isSecure: Bool
    ) async {
        isLoading = true
        defer { isLoading = false }
        
        let folder = KnowledgeFolder(
            id: UUID(),
            name: name,
            description: description,
            icon: icon,
            color: color,
            isSecure: isSecure,
            documents: [],
            createdAt: Date(),
            updatedAt: Date()
        )
        
        do {
            // Save to Supabase
            let folderData = KnowledgeFolderData(
                id: folder.id.uuidString,
                name: folder.name,
                description: folder.description,
                icon: folder.icon,
                colorHex: folder.color.toHex(),
                isSecure: folder.isSecure,
                userId: getCurrentUserId().uuidString,
                createdAt: folder.createdAt,
                updatedAt: folder.updatedAt
            )
            
            try await supabase
                .from("knowledge_folders")
                .insert(folderData)
                .execute()
            
            // Add to local array
            folders.append(folder)
            
        } catch {
            errorMessage = "Failed to create folder: \(error.localizedDescription)"
        }
    }
    
    func deleteFolder(_ folder: KnowledgeFolder) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // Delete from Supabase
            try await supabase
                .from("knowledge_folders")
                .delete()
                .eq("id", value: folder.id.uuidString)
                .execute()
            
            // Remove from local array
            folders.removeAll { $0.id == folder.id }
            
        } catch {
            errorMessage = "Failed to delete folder: \(error.localizedDescription)"
        }
    }
    
    func addDocument(to folder: KnowledgeFolder, document: KnowledgeDocument) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // Save document to Supabase
            let documentData = KnowledgeDocumentData(
                id: document.id.uuidString,
                folderId: folder.id.uuidString,
                title: document.title,
                content: document.content,
                type: document.type.rawValue,
                fileURL: document.fileURL,
                tags: document.tags,
                createdAt: document.createdAt,
                updatedAt: document.updatedAt
            )
            
            try await supabase
                .from("knowledge_documents")
                .insert(documentData)
                .execute()
            
            // Update local folder
            if let index = folders.firstIndex(where: { $0.id == folder.id }) {
                folders[index].documents.append(document)
            }
            
        } catch {
            errorMessage = "Failed to add document: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Search
    
    func searchDocuments(query: String) -> [KnowledgeDocument] {
        var results: [KnowledgeDocument] = []
        
        for folder in folders {
            let matchingDocs = folder.documents.filter { document in
                document.title.localizedCaseInsensitiveContains(query) ||
                document.content.localizedCaseInsensitiveContains(query) ||
                document.tags.contains { $0.localizedCaseInsensitiveContains(query) }
            }
            results.append(contentsOf: matchingDocs)
        }
        
        return results
    }
    
    // MARK: - Private Methods
    
    private func loadFolders() async {
        guard let userId = getCurrentUserId() else { return }
        
        do {
            let response: [KnowledgeFolderData] = try await supabase
                .from("knowledge_folders")
                .select()
                .eq("user_id", value: userId.uuidString)
                .execute()
                .value
            
            var loadedFolders: [KnowledgeFolder] = []
            
            for folderData in response {
                // Load documents for this folder
                let documentsResponse: [KnowledgeDocumentData] = try await supabase
                    .from("knowledge_documents")
                    .select()
                    .eq("folder_id", value: folderData.id)
                    .execute()
                    .value
                
                let documents = documentsResponse.compactMap { docData in
                    KnowledgeDocument(
                        id: UUID(uuidString: docData.id) ?? UUID(),
                        title: docData.title,
                        content: docData.content,
                        type: KnowledgeDocumentType(rawValue: docData.type) ?? .text,
                        fileURL: docData.fileURL,
                        tags: docData.tags,
                        createdAt: docData.createdAt,
                        updatedAt: docData.updatedAt
                    )
                }
                
                let folder = KnowledgeFolder(
                    id: UUID(uuidString: folderData.id) ?? UUID(),
                    name: folderData.name,
                    description: folderData.description,
                    icon: folderData.icon,
                    color: Color(hex: folderData.colorHex) ?? .blue,
                    isSecure: folderData.isSecure,
                    documents: documents,
                    createdAt: folderData.createdAt,
                    updatedAt: folderData.updatedAt
                )
                
                loadedFolders.append(folder)
            }
            
            folders = loadedFolders
            
        } catch {
            errorMessage = "Failed to load folders: \(error.localizedDescription)"
        }
    }
    
    private func getCurrentUserId() -> UUID {
        return UUID(uuidString: "dev-user-123") ?? UUID() // Development mode
    }
}

// MARK: - Data Models

struct KnowledgeFolder: Identifiable {
    let id: UUID
    let name: String
    let description: String
    let icon: String
    let color: Color
    let isSecure: Bool
    var documents: [KnowledgeDocument]
    let createdAt: Date
    let updatedAt: Date
}

struct KnowledgeDocument: Identifiable {
    let id: UUID
    let title: String
    let content: String
    let type: KnowledgeDocumentType
    let fileURL: String?
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date
}

enum KnowledgeDocumentType: String, CaseIterable {
    case text = "text"
    case recipe = "recipe"
    case medical = "medical"
    case training = "training"
    case photo = "photo"
    case document = "document"
    
    var displayName: String {
        switch self {
        case .text: return "Text Note"
        case .recipe: return "Recipe"
        case .medical: return "Medical Record"
        case .training: return "Training Guide"
        case .photo: return "Photo"
        case .document: return "Document"
        }
    }
    
    var icon: String {
        switch self {
        case .text: return "doc.text.fill"
        case .recipe: return "leaf.fill"
        case .medical: return "stethoscope"
        case .training: return "graduationcap.fill"
        case .photo: return "photo.fill"
        case .document: return "doc.fill"
        }
    }
}

// MARK: - Supabase Data Models

struct KnowledgeFolderData: Codable {
    let id: String
    let name: String
    let description: String
    let icon: String
    let colorHex: String
    let isSecure: Bool
    let userId: String
    let createdAt: Date
    let updatedAt: Date
}

struct KnowledgeDocumentData: Codable {
    let id: String
    let folderId: String
    let title: String
    let content: String
    let type: String
    let fileURL: String?
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date
}

// MARK: - Color Extensions

extension Color {
    func toHex() -> String {
        let uiColor = UIColor(self)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        return String(format: "#%02X%02X%02X",
                     Int(red * 255),
                     Int(green * 255),
                     Int(blue * 255))
    }
    
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
