//
//  SecureVaultService.swift
//  PetCapsule
//
//  Secure vault system with password protection and Supabase integration
//

import Foundation
import SwiftUI
import Supabase
import CryptoKit

@MainActor
class SecureVaultService: ObservableObject {
    static let shared = SecureVaultService()
    
    @Published var vaults: [SecureVault] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabase = SupabaseClient(
        supabaseURL: URL(string: Config.Supabase.url)!,
        supabaseKey: Config.Supabase.anonKey
    )
    
    private init() {
        Task {
            await loadUserVaults()
        }
    }
    
    // MARK: - Vault Management
    
    func createVault(
        name: String,
        description: String,
        password: String,
        securityQuestion: String,
        securityAnswer: String,
        userId: UUID
    ) async throws -> SecureVault {
        isLoading = true
        defer { isLoading = false }
        
        // Hash password and security answer
        let passwordHash = hashPassword(password)
        let answerHash = hashPassword(securityAnswer.lowercased().trimmingCharacters(in: .whitespacesAndNewlines))
        
        let vault = SecureVault(
            id: UUID(),
            name: name,
            description: description,
            passwordHash: passwordHash,
            securityQuestion: securityQuestion,
            securityAnswerHash: answerHash,
            userId: userId,
            createdAt: Date(),
            updatedAt: Date(),
            memoryCount: 0,
            isLocked: true
        )
        
        // Save to Supabase
        let vaultData = VaultCreateData(
            id: vault.id.uuidString,
            name: vault.name,
            description: vault.description,
            passwordHash: vault.passwordHash,
            securityQuestion: vault.securityQuestion,
            securityAnswerHash: vault.securityAnswerHash,
            userId: vault.userId.uuidString,
            createdAt: vault.createdAt,
            updatedAt: vault.updatedAt
        )
        
        try await supabase
            .from("secure_vaults")
            .insert(vaultData)
            .execute()
        
        // Add to local array
        vaults.append(vault)
        
        return vault
    }
    
    func unlockVault(_ vault: SecureVault, password: String) -> Bool {
        let passwordHash = hashPassword(password)
        return passwordHash == vault.passwordHash
    }
    
    func unlockVaultWithSecurityAnswer(_ vault: SecureVault, answer: String) -> Bool {
        let answerHash = hashPassword(answer.lowercased().trimmingCharacters(in: .whitespacesAndNewlines))
        return answerHash == vault.securityAnswerHash
    }
    
    func deleteVault(_ vault: SecureVault) async throws {
        isLoading = true
        defer { isLoading = false }
        
        // Delete from Supabase
        try await supabase
            .from("secure_vaults")
            .delete()
            .eq("id", value: vault.id.uuidString)
            .execute()
        
        // Remove from local array
        vaults.removeAll { $0.id == vault.id }
    }
    
    // MARK: - Memory Management
    
    func addMemoryToVault(_ memory: Memory, vault: SecureVault, password: String) async throws {
        guard unlockVault(vault, password: password) else {
            throw VaultError.invalidPassword
        }
        
        // Encrypt memory data
        let encryptedData = try encryptMemoryData(memory, password: password)
        
        let vaultMemory = VaultMemoryData(
            id: UUID().uuidString,
            vaultId: vault.id.uuidString,
            memoryId: memory.id.uuidString,
            encryptedData: encryptedData,
            createdAt: Date()
        )
        
        try await supabase
            .from("vault_memories")
            .insert(vaultMemory)
            .execute()
        
        // Update vault memory count
        if let index = vaults.firstIndex(where: { $0.id == vault.id }) {
            vaults[index].memoryCount += 1
        }
    }
    
    func getVaultMemories(_ vault: SecureVault, password: String) async throws -> [Memory] {
        guard unlockVault(vault, password: password) else {
            throw VaultError.invalidPassword
        }
        
        let response: [VaultMemoryData] = try await supabase
            .from("vault_memories")
            .select()
            .eq("vault_id", value: vault.id.uuidString)
            .execute()
            .value
        
        var memories: [Memory] = []
        for vaultMemory in response {
            if let memory = try? decryptMemoryData(vaultMemory.encryptedData, password: password) {
                memories.append(memory)
            }
        }
        
        return memories
    }
    
    // MARK: - Private Methods
    
    private func loadUserVaults() async {
        guard let userId = getCurrentUserId() else { return }
        
        do {
            let response: [VaultData] = try await supabase
                .from("secure_vaults")
                .select()
                .eq("user_id", value: userId.uuidString)
                .execute()
                .value
            
            vaults = response.compactMap { data in
                SecureVault(
                    id: UUID(uuidString: data.id) ?? UUID(),
                    name: data.name,
                    description: data.description,
                    passwordHash: data.passwordHash,
                    securityQuestion: data.securityQuestion,
                    securityAnswerHash: data.securityAnswerHash,
                    userId: UUID(uuidString: data.userId) ?? UUID(),
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt,
                    memoryCount: data.memoryCount ?? 0,
                    isLocked: true
                )
            }
        } catch {
            errorMessage = "Failed to load vaults: \(error.localizedDescription)"
        }
    }
    
    private func getCurrentUserId() -> UUID? {
        // Get current user ID from auth service
        return UUID(uuidString: "dev-user-123") // Development mode
    }
    
    private func hashPassword(_ password: String) -> String {
        let data = Data(password.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    private func encryptMemoryData(_ memory: Memory, password: String) throws -> String {
        let encoder = JSONEncoder()
        let memoryData = try encoder.encode(memory)
        
        // Simple encryption using password hash as key
        let key = hashPassword(password)
        let encryptedData = memoryData.base64EncodedString() + ":" + key.prefix(16)
        
        return encryptedData
    }
    
    private func decryptMemoryData(_ encryptedData: String, password: String) throws -> Memory {
        let components = encryptedData.components(separatedBy: ":")
        guard components.count == 2 else {
            throw VaultError.decryptionFailed
        }
        
        let key = hashPassword(password)
        guard components[1] == key.prefix(16) else {
            throw VaultError.invalidPassword
        }
        
        guard let data = Data(base64Encoded: components[0]) else {
            throw VaultError.decryptionFailed
        }
        
        let decoder = JSONDecoder()
        return try decoder.decode(Memory.self, from: data)
    }
}

// MARK: - Data Models

struct SecureVault: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let passwordHash: String
    let securityQuestion: String
    let securityAnswerHash: String
    let userId: UUID
    let createdAt: Date
    let updatedAt: Date
    var memoryCount: Int
    var isLocked: Bool
}

struct VaultCreateData: Codable {
    let id: String
    let name: String
    let description: String
    let passwordHash: String
    let securityQuestion: String
    let securityAnswerHash: String
    let userId: String
    let createdAt: Date
    let updatedAt: Date
}

struct VaultData: Codable {
    let id: String
    let name: String
    let description: String
    let passwordHash: String
    let securityQuestion: String
    let securityAnswerHash: String
    let userId: String
    let createdAt: Date
    let updatedAt: Date
    let memoryCount: Int?
}

struct VaultMemoryData: Codable {
    let id: String
    let vaultId: String
    let memoryId: String
    let encryptedData: String
    let createdAt: Date
}

enum VaultError: Error, LocalizedError {
    case invalidPassword
    case decryptionFailed
    case vaultNotFound
    
    var errorDescription: String? {
        switch self {
        case .invalidPassword:
            return "Invalid password or security answer"
        case .decryptionFailed:
            return "Failed to decrypt vault data"
        case .vaultNotFound:
            return "Vault not found"
        }
    }
}
