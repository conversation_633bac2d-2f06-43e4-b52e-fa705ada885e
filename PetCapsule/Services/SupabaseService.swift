//
//  SupabaseService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Supabase

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()

    let client: SupabaseClient

    @Published var currentUser: User?
    @Published var isAuthenticated = false

    private init() {
        // Initialize Supabase client with configuration
        self.client = SupabaseClient(
            supabaseURL: URL(string: Config.Supabase.url)!,
            supabaseKey: Config.Supabase.anonKey
        )

        // Listen for auth state changes
        Task {
            await listenForAuthChanges()
        }
    }

    private func listenForAuthChanges() async {
        for await state in client.auth.authStateChanges {
            await MainActor.run {
                switch state.event {
                case .signedIn:
                    self.isAuthenticated = true
                    Task {
                        await self.loadCurrentUser()
                    }
                case .signedOut:
                    self.isAuthenticated = false
                    self.currentUser = nil
                default:
                    break
                }
            }
        }
    }

    // MARK: - Authentication

    func signInWithApple() async throws {
        // TODO: Implement Apple Sign-In
        // This would integrate with Apple's Sign in with Apple
        throw NSError(domain: "SupabaseService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Apple Sign-In not implemented yet"])
    }

    func signInWithEmail(_ email: String, password: String) async throws {
        try await client.auth.signIn(email: email, password: password)
    }

    func signUpWithEmail(_ email: String, password: String, displayName: String) async throws {
        let response = try await client.auth.signUp(email: email, password: password)

        // Create user profile in database
        let newUser = User(
            id: response.user.id.uuidString,
            email: email,
            displayName: displayName
        )

        try await createUserProfile(newUser)
    }

    func signOut() async throws {
        try await client.auth.signOut()
    }

    // MARK: - User Management

    private func loadCurrentUser() async {
        guard let authUser = client.auth.currentUser else { return }

        do {
            // Use a sendable data structure instead of User directly
            struct UserData: Codable, Sendable {
                let id: String
                let email: String
                let displayName: String
                let profileImageURL: String?
                let subscriptionTier: String
                let memoryGems: Int
                let totalUploads: Int
                let totalVaults: Int
                let joinedNetworkAt: Date?
                let createdAt: Date
                let updatedAt: Date
                let lastActiveAt: Date

                enum CodingKeys: String, CodingKey {
                    case id, email
                    case displayName = "display_name"
                    case profileImageURL = "profile_image_url"
                    case subscriptionTier = "subscription_tier"
                    case memoryGems = "memory_gems"
                    case totalUploads = "total_uploads"
                    case totalVaults = "total_vaults"
                    case joinedNetworkAt = "joined_network_at"
                    case createdAt = "created_at"
                    case updatedAt = "updated_at"
                    case lastActiveAt = "last_active_at"
                }
            }

            let response: [UserData] = try await client
                .from("users")
                .select()
                .eq("id", value: authUser.id.uuidString)
                .execute()
                .value

            await MainActor.run {
                if let userData = response.first {
                    // Convert UserData to User model
                    let user = User(
                        id: userData.id,
                        email: userData.email,
                        displayName: userData.displayName,
                        profileImageURL: userData.profileImageURL,
                        subscriptionTier: SubscriptionTier(rawValue: userData.subscriptionTier) ?? .free
                    )
                    user.memoryGems = userData.memoryGems
                    user.totalUploads = userData.totalUploads
                    user.totalVaults = userData.totalVaults
                    user.joinedNetworkAt = userData.joinedNetworkAt
                    user.createdAt = userData.createdAt
                    user.updatedAt = userData.updatedAt
                    user.lastActiveAt = userData.lastActiveAt

                    self.currentUser = user
                }
            }
        } catch {
            print("Error loading current user: \(error)")
        }
    }

    private func createUserProfile(_ user: User) async throws {
        // Create a sendable data structure for the user
        struct UserInsertData: Codable, Sendable {
            let id: String
            let email: String
            let displayName: String
            let profileImageURL: String?
            let subscriptionTier: String
            let memoryGems: Int
            let totalUploads: Int
            let totalVaults: Int
            let joinedNetworkAt: Date?
            let createdAt: Date
            let updatedAt: Date
            let lastActiveAt: Date

            enum CodingKeys: String, CodingKey {
                case id, email
                case displayName = "display_name"
                case profileImageURL = "profile_image_url"
                case subscriptionTier = "subscription_tier"
                case memoryGems = "memory_gems"
                case totalUploads = "total_uploads"
                case totalVaults = "total_vaults"
                case joinedNetworkAt = "joined_network_at"
                case createdAt = "created_at"
                case updatedAt = "updated_at"
                case lastActiveAt = "last_active_at"
            }
        }

        let userData = UserInsertData(
            id: user.id,
            email: user.email,
            displayName: user.displayName,
            profileImageURL: user.profileImageURL,
            subscriptionTier: user.subscriptionTier.rawValue,
            memoryGems: user.memoryGems,
            totalUploads: user.totalUploads,
            totalVaults: user.totalVaults,
            joinedNetworkAt: user.joinedNetworkAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastActiveAt: user.lastActiveAt
        )

        try await client
            .from("users")
            .insert(userData)
            .execute()
    }

    // MARK: - Pet Management

    func createPet(_ pet: Pet) async throws {
        // Create a sendable data structure for the pet
        struct PetInsertData: Codable, Sendable {
            let id: String
            let name: String
            let species: String
            let breed: String
            let age: Int
            let dateOfBirth: Date?
            let profileImageURL: String?
            let bio: String
            let isDeceased: Bool
            let dateOfPassing: Date?
            let ownerID: String
            let createdAt: Date
            let updatedAt: Date

            enum CodingKeys: String, CodingKey {
                case id, name, species, breed, age, bio
                case dateOfBirth = "date_of_birth"
                case profileImageURL = "profile_image_url"
                case isDeceased = "is_deceased"
                case dateOfPassing = "date_of_passing"
                case ownerID = "owner_id"
                case createdAt = "created_at"
                case updatedAt = "updated_at"
            }
        }

        let petData = PetInsertData(
            id: pet.id.uuidString,
            name: pet.name,
            species: pet.species,
            breed: pet.breed,
            age: pet.age,
            dateOfBirth: pet.dateOfBirth,
            profileImageURL: pet.profileImageURL,
            bio: pet.bio,
            isDeceased: pet.isDeceased,
            dateOfPassing: pet.dateOfPassing,
            ownerID: pet.ownerID,
            createdAt: pet.createdAt,
            updatedAt: pet.updatedAt
        )

        try await client
            .from("pets")
            .insert(petData)
            .execute()
    }

    func fetchUserPets() async throws -> [Pet] {
        guard let currentUser = currentUser else { return [] }

        // Create a sendable data structure for fetching pets
        struct PetData: Codable, Sendable {
            let id: String
            let name: String
            let species: String
            let breed: String
            let age: Int
            let dateOfBirth: Date?
            let profileImageURL: String?
            let bio: String
            let isDeceased: Bool
            let dateOfPassing: Date?
            let ownerID: String
            let createdAt: Date
            let updatedAt: Date

            enum CodingKeys: String, CodingKey {
                case id, name, species, breed, age, bio
                case dateOfBirth = "date_of_birth"
                case profileImageURL = "profile_image_url"
                case isDeceased = "is_deceased"
                case dateOfPassing = "date_of_passing"
                case ownerID = "owner_id"
                case createdAt = "created_at"
                case updatedAt = "updated_at"
            }
        }

        let response: [PetData] = try await client
            .from("pets")
            .select()
            .eq("owner_id", value: currentUser.id)
            .execute()
            .value

        // Convert PetData to Pet models
        return response.map { petData in
            Pet(
                name: petData.name,
                species: petData.species,
                breed: petData.breed,
                age: petData.age,
                dateOfBirth: petData.dateOfBirth,
                profileImageURL: petData.profileImageURL,
                bio: petData.bio,
                isDeceased: petData.isDeceased,
                dateOfPassing: petData.dateOfPassing,
                ownerID: petData.ownerID
            )
        }
    }

    func updatePet(_ pet: Pet) async throws {
        // Create a sendable data structure for updating pets
        struct PetUpdateData: Codable, Sendable {
            let name: String
            let species: String
            let breed: String
            let age: Int
            let dateOfBirth: Date?
            let profileImageURL: String?
            let bio: String
            let isDeceased: Bool
            let dateOfPassing: Date?
            let updatedAt: Date

            enum CodingKeys: String, CodingKey {
                case name, species, breed, age, bio
                case dateOfBirth = "date_of_birth"
                case profileImageURL = "profile_image_url"
                case isDeceased = "is_deceased"
                case dateOfPassing = "date_of_passing"
                case updatedAt = "updated_at"
            }
        }

        let petUpdateData = PetUpdateData(
            name: pet.name,
            species: pet.species,
            breed: pet.breed,
            age: pet.age,
            dateOfBirth: pet.dateOfBirth,
            profileImageURL: pet.profileImageURL,
            bio: pet.bio,
            isDeceased: pet.isDeceased,
            dateOfPassing: pet.dateOfPassing,
            updatedAt: Date()
        )

        try await client
            .from("pets")
            .update(petUpdateData)
            .eq("id", value: pet.id.uuidString)
            .execute()
    }

    func deletePet(_ petId: UUID) async throws {
        try await client
            .from("pets")
            .delete()
            .eq("id", value: petId.uuidString)
            .execute()
    }
}
