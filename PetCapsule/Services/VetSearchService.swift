//
//  VetSearchService.swift
//  PetCapsule
//
//  Service for searching and managing veterinarian information
//

import Foundation
import SwiftUI
import CoreLocation
import MapKit

@MainActor
class VetSearchService: ObservableObject {
    static let shared = VetSearchService()
    
    @Published var veterinarians: [VeterinarianInfo] = []
    @Published var savedVets: [VeterinarianInfo] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let locationManager = CLLocationManager()
    private var currentLocation: CLLocation?
    
    private init() {
        setupLocationManager()
        loadMockVets() // For development
    }
    
    // MARK: - Location Management
    
    private func setupLocationManager() {
        locationManager.requestWhenInUseAuthorization()
        locationManager.startUpdatingLocation()
    }
    
    // MARK: - Search Methods
    
    func searchNearbyVets() async {
        isLoading = true
        defer { isLoading = false }
        
        // In a real app, this would use Google Places API or similar
        // For now, we'll use mock data with realistic information
        
        await MainActor.run {
            veterinarians = generateMockVets()
        }
    }
    
    func searchVetsByName(_ name: String) async {
        isLoading = true
        defer { isLoading = false }
        
        // Filter existing vets by name
        let filtered = veterinarians.filter { vet in
            vet.name.localizedCaseInsensitiveContains(name) ||
            vet.clinicName.localizedCaseInsensitiveContains(name)
        }
        
        await MainActor.run {
            veterinarians = filtered
        }
    }
    
    func saveVet(_ vet: VeterinarianInfo) {
        if !savedVets.contains(where: { $0.id == vet.id }) {
            savedVets.append(vet)
            // In a real app, save to UserDefaults or database
        }
    }
    
    func removeSavedVet(_ vet: VeterinarianInfo) {
        savedVets.removeAll { $0.id == vet.id }
    }
    
    // MARK: - Mock Data Generation
    
    private func loadMockVets() {
        veterinarians = generateMockVets()
    }
    
    private func generateMockVets() -> [VeterinarianInfo] {
        let mockVets = [
            VeterinarianInfo(
                id: UUID(),
                name: "Dr. Sarah Johnson",
                clinicName: "Paws & Claws Veterinary Hospital",
                address: "123 Main St, San Francisco, CA 94102",
                phoneNumber: "(*************",
                email: "<EMAIL>",
                website: "www.pawsandclaws.com",
                coordinate: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
                distance: 0.8,
                rating: 4.8,
                reviewCount: 245,
                specialties: ["Emergency Care", "Surgery", "Dental"],
                services: ["Wellness Exams", "Vaccinations", "Surgery", "Dental Care", "Emergency Care"],
                hours: [
                    "Monday: 8:00 AM - 6:00 PM",
                    "Tuesday: 8:00 AM - 6:00 PM",
                    "Wednesday: 8:00 AM - 6:00 PM",
                    "Thursday: 8:00 AM - 6:00 PM",
                    "Friday: 8:00 AM - 6:00 PM",
                    "Saturday: 9:00 AM - 4:00 PM",
                    "Sunday: Closed"
                ],
                isOpen: true,
                isEmergency: true,
                acceptsInsurance: true,
                languages: ["English", "Spanish"]
            ),
            
            VeterinarianInfo(
                id: UUID(),
                name: "Dr. Michael Chen",
                clinicName: "Bay Area Animal Clinic",
                address: "456 Oak Ave, San Francisco, CA 94110",
                phoneNumber: "(*************",
                email: "<EMAIL>",
                website: "www.bayareaanimal.com",
                coordinate: CLLocationCoordinate2D(latitude: 37.7849, longitude: -122.4094),
                distance: 1.2,
                rating: 4.6,
                reviewCount: 189,
                specialties: ["Internal Medicine", "Cardiology"],
                services: ["Wellness Exams", "Cardiology", "Internal Medicine", "Diagnostics"],
                hours: [
                    "Monday: 9:00 AM - 5:00 PM",
                    "Tuesday: 9:00 AM - 5:00 PM",
                    "Wednesday: 9:00 AM - 5:00 PM",
                    "Thursday: 9:00 AM - 5:00 PM",
                    "Friday: 9:00 AM - 5:00 PM",
                    "Saturday: 10:00 AM - 2:00 PM",
                    "Sunday: Closed"
                ],
                isOpen: true,
                isEmergency: false,
                acceptsInsurance: true,
                languages: ["English", "Mandarin"]
            ),
            
            VeterinarianInfo(
                id: UUID(),
                name: "Dr. Emily Rodriguez",
                clinicName: "Mission Pet Hospital",
                address: "789 Mission St, San Francisco, CA 94103",
                phoneNumber: "(*************",
                email: "<EMAIL>",
                website: "www.missionpet.com",
                coordinate: CLLocationCoordinate2D(latitude: 37.7649, longitude: -122.4294),
                distance: 2.1,
                rating: 4.9,
                reviewCount: 312,
                specialties: ["Exotic Animals", "Dermatology", "Behavior"],
                services: ["Exotic Care", "Dermatology", "Behavior Consultation", "Grooming"],
                hours: [
                    "Monday: 8:00 AM - 7:00 PM",
                    "Tuesday: 8:00 AM - 7:00 PM",
                    "Wednesday: 8:00 AM - 7:00 PM",
                    "Thursday: 8:00 AM - 7:00 PM",
                    "Friday: 8:00 AM - 7:00 PM",
                    "Saturday: 9:00 AM - 5:00 PM",
                    "Sunday: 10:00 AM - 3:00 PM"
                ],
                isOpen: false,
                isEmergency: false,
                acceptsInsurance: true,
                languages: ["English", "Spanish", "Portuguese"]
            ),
            
            VeterinarianInfo(
                id: UUID(),
                name: "Dr. James Wilson",
                clinicName: "24/7 Emergency Animal Hospital",
                address: "321 Emergency Blvd, San Francisco, CA 94105",
                phoneNumber: "(*************",
                email: "<EMAIL>",
                website: "www.24vetcare.com",
                coordinate: CLLocationCoordinate2D(latitude: 37.7949, longitude: -122.3994),
                distance: 3.5,
                rating: 4.4,
                reviewCount: 156,
                specialties: ["Emergency Medicine", "Critical Care", "Surgery"],
                services: ["24/7 Emergency Care", "Critical Care", "Emergency Surgery", "Trauma Care"],
                hours: [
                    "Open 24/7"
                ],
                isOpen: true,
                isEmergency: true,
                acceptsInsurance: true,
                languages: ["English"]
            ),
            
            VeterinarianInfo(
                id: UUID(),
                name: "Dr. Lisa Park",
                clinicName: "Holistic Pet Wellness Center",
                address: "654 Wellness Way, San Francisco, CA 94107",
                phoneNumber: "(*************",
                email: "<EMAIL>",
                website: "www.holisticpetwellness.com",
                coordinate: CLLocationCoordinate2D(latitude: 37.7549, longitude: -122.4394),
                distance: 4.2,
                rating: 4.7,
                reviewCount: 98,
                specialties: ["Holistic Medicine", "Acupuncture", "Nutrition"],
                services: ["Holistic Care", "Acupuncture", "Nutrition Counseling", "Herbal Medicine"],
                hours: [
                    "Monday: 10:00 AM - 6:00 PM",
                    "Tuesday: 10:00 AM - 6:00 PM",
                    "Wednesday: Closed",
                    "Thursday: 10:00 AM - 6:00 PM",
                    "Friday: 10:00 AM - 6:00 PM",
                    "Saturday: 11:00 AM - 4:00 PM",
                    "Sunday: Closed"
                ],
                isOpen: true,
                isEmergency: false,
                acceptsInsurance: false,
                languages: ["English", "Korean"]
            )
        ]
        
        return mockVets
    }
}

// MARK: - Data Models

struct VeterinarianInfo: Identifiable {
    let id: UUID
    let name: String
    let clinicName: String
    let address: String
    let phoneNumber: String
    let email: String
    let website: String
    let coordinate: CLLocationCoordinate2D
    let distance: Double // in miles
    let rating: Double
    let reviewCount: Int
    let specialties: [String]
    let services: [String]
    let hours: [String]
    let isOpen: Bool
    let isEmergency: Bool
    let acceptsInsurance: Bool
    let languages: [String]
}

// MARK: - Vet Detail View

struct VetDetailView: View {
    let vet: VeterinarianInfo
    @EnvironmentObject var vetService: VetSearchService
    @Environment(\.dismiss) private var dismiss
    @State private var isSaved = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(vet.name)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                
                                Text(vet.clinicName)
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 4) {
                                HStack {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.yellow)
                                    Text(String(format: "%.1f", vet.rating))
                                        .fontWeight(.medium)
                                }
                                
                                Text("\(vet.reviewCount) reviews")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // Status and Distance
                        HStack {
                            HStack {
                                Circle()
                                    .fill(vet.isOpen ? Color.green : Color.red)
                                    .frame(width: 8, height: 8)
                                Text(vet.isOpen ? "Open" : "Closed")
                                    .font(.subheadline)
                                    .foregroundColor(vet.isOpen ? .green : .red)
                            }
                            
                            Text("•")
                                .foregroundColor(.secondary)
                            
                            Text("\(String(format: "%.1f", vet.distance)) miles away")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            if vet.isEmergency {
                                Text("•")
                                    .foregroundColor(.secondary)
                                
                                Text("Emergency")
                                    .font(.subheadline)
                                    .foregroundColor(.red)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                    
                    // Contact Actions
                    HStack(spacing: 12) {
                        Button(action: callVet) {
                            HStack {
                                Image(systemName: "phone.fill")
                                Text("Call")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        
                        Button(action: openDirections) {
                            HStack {
                                Image(systemName: "location.fill")
                                Text("Directions")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                    }
                    
                    // Specialties
                    if !vet.specialties.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Specialties")
                                .font(.headline)
                                .fontWeight(.bold)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                                ForEach(vet.specialties, id: \.self) { specialty in
                                    Text(specialty)
                                        .font(.subheadline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.green.opacity(0.1))
                                        .foregroundColor(.green)
                                        .cornerRadius(8)
                                }
                            }
                        }
                    }
                    
                    // Hours
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Hours")
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(vet.hours, id: \.self) { hour in
                                Text(hour)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    // Contact Info
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Contact Information")
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "location")
                                    .foregroundColor(.secondary)
                                Text(vet.address)
                                    .font(.subheadline)
                            }
                            
                            HStack {
                                Image(systemName: "phone")
                                    .foregroundColor(.secondary)
                                Text(vet.phoneNumber)
                                    .font(.subheadline)
                            }
                            
                            HStack {
                                Image(systemName: "envelope")
                                    .foregroundColor(.secondary)
                                Text(vet.email)
                                    .font(.subheadline)
                            }
                            
                            HStack {
                                Image(systemName: "globe")
                                    .foregroundColor(.secondary)
                                Text(vet.website)
                                    .font(.subheadline)
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Veterinarian Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isSaved ? "Saved" : "Save") {
                        if isSaved {
                            vetService.removeSavedVet(vet)
                        } else {
                            vetService.saveVet(vet)
                        }
                        isSaved.toggle()
                    }
                    .foregroundColor(isSaved ? .green : .blue)
                }
            }
            .onAppear {
                isSaved = vetService.savedVets.contains { $0.id == vet.id }
            }
        }
    }
    
    private func callVet() {
        if let url = URL(string: "tel:\(vet.phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openDirections() {
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: vet.coordinate))
        mapItem.name = vet.clinicName
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving])
    }
}
