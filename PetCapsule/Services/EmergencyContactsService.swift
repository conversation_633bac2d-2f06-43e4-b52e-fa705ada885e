//
//  EmergencyContactsService.swift
//  PetCapsule
//
//  Service for managing emergency contacts
//

import Foundation
import SwiftUI

@MainActor
class EmergencyContactsService: ObservableObject {
    static let shared = EmergencyContactsService()
    
    @Published var contacts: [EmergencyContact] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let userDefaults = UserDefaults.standard
    private let contactsKey = "emergency_contacts"
    
    private init() {
        loadContacts()
    }
    
    // MARK: - Contact Management
    
    func addContact(name: String, phoneNumber: String, type: EmergencyContactType, notes: String) async {
        let contact = EmergencyContact(
            id: UUID(),
            name: name,
            phoneNumber: phoneNumber,
            type: type,
            notes: notes,
            createdAt: Date()
        )
        
        contacts.append(contact)
        saveContacts()
    }
    
    func deleteContact(_ contact: EmergencyContact) {
        contacts.removeAll { $0.id == contact.id }
        saveContacts()
    }
    
    func updateContact(_ contact: EmergencyContact) {
        if let index = contacts.firstIndex(where: { $0.id == contact.id }) {
            contacts[index] = contact
            saveContacts()
        }
    }
    
    // MARK: - Persistence
    
    private func saveContacts() {
        do {
            let data = try JSONEncoder().encode(contacts)
            userDefaults.set(data, forKey: contactsKey)
        } catch {
            errorMessage = "Failed to save contacts: \(error.localizedDescription)"
        }
    }
    
    private func loadContacts() {
        guard let data = userDefaults.data(forKey: contactsKey) else {
            // Load default emergency contacts
            loadDefaultContacts()
            return
        }
        
        do {
            contacts = try JSONDecoder().decode([EmergencyContact].self, from: data)
        } catch {
            errorMessage = "Failed to load contacts: \(error.localizedDescription)"
            loadDefaultContacts()
        }
    }
    
    private func loadDefaultContacts() {
        // Add some default emergency contacts
        contacts = []
    }
}

// MARK: - Data Models

struct EmergencyContact: Identifiable, Codable {
    let id: UUID
    let name: String
    let phoneNumber: String
    let type: EmergencyContactType
    let notes: String
    let createdAt: Date
}

enum EmergencyContactType: String, CaseIterable, Codable {
    case veterinarian = "veterinarian"
    case emergencyVet = "emergency_vet"
    case petSitter = "pet_sitter"
    case groomer = "groomer"
    case trainer = "trainer"
    case petStore = "pet_store"
    case insurance = "insurance"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .veterinarian:
            return "Veterinarian"
        case .emergencyVet:
            return "Emergency Vet"
        case .petSitter:
            return "Pet Sitter"
        case .groomer:
            return "Groomer"
        case .trainer:
            return "Trainer"
        case .petStore:
            return "Pet Store"
        case .insurance:
            return "Pet Insurance"
        case .other:
            return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .veterinarian:
            return "stethoscope"
        case .emergencyVet:
            return "cross.case.fill"
        case .petSitter:
            return "person.fill"
        case .groomer:
            return "scissors"
        case .trainer:
            return "graduationcap.fill"
        case .petStore:
            return "storefront.fill"
        case .insurance:
            return "shield.fill"
        case .other:
            return "person.crop.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .veterinarian:
            return .green
        case .emergencyVet:
            return .red
        case .petSitter:
            return .blue
        case .groomer:
            return .purple
        case .trainer:
            return .orange
        case .petStore:
            return .brown
        case .insurance:
            return .indigo
        case .other:
            return .gray
        }
    }
}
